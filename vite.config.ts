import { defineConfig } from 'vite'
import RubyPlugin from 'vite-plugin-ruby'
import vue from '@vitejs/plugin-vue'
import { consoleLoggerPlugin } from './vite-console-plugin.js'
// REVERT: Remove this import and plugin below to uninstall vite-plugin-vue-mcp
import { VueMcp } from 'vite-plugin-vue-mcp'

export default defineConfig(({ mode }) => ({
  plugins: [
    RubyPlugin(),
    vue(),
    consoleLoggerPlugin(),
    VueMcp()
  ],
  server: {
    host: '0.0.0.0',
    port: 3036,
    strictPort: true,
    hmr: {
      overlay: true,
      host: '************',
      port: 3036,
      clientPort: 3036
    },
    cors: true
  },
  build: {
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: true
  },
  esbuild: {
    drop: mode === 'production' ? ['console', 'debugger'] : [],
  },
  optimizeDeps: {
    include: ['vue', 'axios', 'vue-i18n', '@vuepic/vue-datepicker', 'dayjs', 'lucide-vue-next'],
    force: true
  }
}))
