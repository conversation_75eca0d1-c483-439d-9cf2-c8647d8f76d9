# app/services/jwt_service.rb
class JwtService
  SECRET_KEY = Rails.application.credentials.jwt_secret
  ALGORITHM = 'HS256'.freeze
  DEFAULT_EXPIRATION_DURATION = 24.hours.freeze
  ACCESS_TOKEN_EXPIRATION_DURATION = 15.minutes.freeze
  REFRESH_TOKEN_EXPIRATION_DURATION = 90.days.freeze

  # Encodes a payload into a JWT token
  #
  # @param payload [Hash] The data to encode
  # @param expiration [Time, Integer, ActiveSupport::Duration]
  #        - Time: Absolute expiration time.
  #        - Integer: Absolute expiration time as epoch seconds.
  #        - ActiveSupport::Duration: Duration from Time.current.
  #        Defaults to DEFAULT_EXPIRATION_DURATION from Time.current.
  # @return [String] The JWT token
  # @raise [ArgumentError] if payload is not a Hash or JWT secret is not configured
  def self.encode(payload, expiration = DEFAULT_EXPIRATION_DURATION)
    raise ArgumentError, "Payload must be a Hash" unless payload.is_a?(Hash)
    raise ArgumentError, "JWT secret not configured" if SECRET_KEY.blank?

    payload_to_encode = payload.dup
    now = Time.current # Consistent timestamp for iat and relative expiration calculation

    exp_timestamp = case expiration
                    when ActiveSupport::Duration
                      (now + expiration).to_i
                    when Time
                      expiration.to_i
                    when Integer
                      expiration # Assumed to be an epoch timestamp
                    else
                      raise ArgumentError, "Invalid expiration type: #{expiration.class}. Must be Time, Integer, or ActiveSupport::Duration."
                    end

    payload_to_encode[:exp] = exp_timestamp
    payload_to_encode[:iat] = now.to_i
    payload_to_encode[:jti] ||= SecureRandom.uuid # Add unique identifier, allow it to be passed in

    JWT.encode(payload_to_encode, SECRET_KEY, ALGORITHM)
  end

  # Decodes a JWT token
  #
  # @param token [String] The JWT token to decode
  # @return [HashWithIndifferentAccess, nil] The decoded payload if the token is valid, nil otherwise
  def self.decode(token)
    decoded = JWT.decode(token, SECRET_KEY, true, { algorithm: ALGORITHM })
    HashWithIndifferentAccess.new(decoded[0])
  rescue JWT::ExpiredSignature
    Rails.logger.warn("JWT expired for token: #{token}")
    nil
  rescue JWT::VerificationError, JWT::DecodeError => e
    Rails.logger.warn("JWT error: #{e.message} for token: #{token}")
    nil
  end

  def self.encode_access_token(payload)
    encode(payload, ACCESS_TOKEN_EXPIRATION_DURATION)
  end

  def self.encode_refresh_token(payload)
    refresh_payload = payload.merge(
      token_type: 'refresh',
      refresh_family_id: payload[:refresh_family_id] || SecureRandom.uuid,
      rotation_count: payload[:rotation_count] || 0
    )
    encode(refresh_payload, REFRESH_TOKEN_EXPIRATION_DURATION)
  end
  
  # Enhanced token rotation for security
  # @param old_refresh_payload [Hash] The decoded refresh token payload
  # @param user [User] The user object
  # @return [String] New refresh token with incremented rotation count
  def self.rotate_refresh_token(old_refresh_payload, user)
    new_payload = user.jwt_payload.merge(
      refresh_family_id: old_refresh_payload['refresh_family_id'],
      rotation_count: (old_refresh_payload['rotation_count'] || 0) + 1
    )
    encode_refresh_token(new_payload)
  end
  
  # Validate refresh token for rotation security
  # @param payload [Hash] The decoded refresh token payload
  # @return [Boolean] Whether token is valid for rotation
  def self.valid_for_rotation?(payload)
    return false unless payload['token_type'] == 'refresh'
    return false unless payload['refresh_family_id'].present?
    
    # Check rotation count for anomaly detection
    rotation_count = payload['rotation_count'] || 0
    if rotation_count > 1000 # Reasonable upper bound
      Rails.logger.warn "Suspicious refresh token rotation count: #{rotation_count}"
      return false
    end
    
    true
  end
end 