<template>
  <div class="grid grid-cols-3 gap-6" data-vue-component="mainbox">
    
    <!-- First Column -->
    <div class="content-spacing">

      <!-- Welcome Section -->
      <div class="card">
        <div class="card-header">
          <h2 class="text-title">{{ formattedDate }}</h2>
          <div class="flex items-center gap-2">
            <RefreshButton target="mainbox" />
            <button @click="toggleWelcomeContent" class="btn-icon-subtle">
              <ChevronUp v-if="!isWelcomeContentCollapsed" :size="18" />
              <ChevronDown v-else :size="18" />
            </button>
          </div>
        </div>
        <div v-show="!isWelcomeContentCollapsed" class="card-content">
          <div class="flex items-center gap-4">
            <div class="flex-1">
              <div class="text-title-lg">{{ greeting }}</div>
              <div v-if="hasPendingItems" class="text-muted">
                {{ $t('you_have_new_pending_items', 'Máte nové {items}', { items: pendingItems }) }}
              </div>
              <div class="flex justify-end gap-4">
                <div>
                  <LocalizedLink :to="'/events'" class="text-link-action blue" :use-anchor="true">
                    {{ $t('show_agenda', 'Zobrazit kalendář') }}
                  </LocalizedLink>
                </div>
                <div>
                  <a
                    href="#"
                    class="text-link-action blue"
                    @click.prevent="openAddEventModal"
                  >
                    {{ $t('add_event', 'Přidat událost') }}
                    `Some comment tryout in mainbox`
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Announcements -->
      <SystemMessage />
      
      <!-- NEWS -->
      <div 
        v-if="hasPendingBookings || hasPendingInvitations"
        class="bg-amber-50 border-l-4 border-amber-500 p-4 mb-4 max-w-[500px]"
        >

        <div v-if="hasPendingBookings">
          <div class="flex items-start mb-2">
            <div class="flex-shrink-0 mr-3">
              <Bell class="text-amber-600" :size="20" />
            </div>
            <div class="flex-1">
              <span class="text-amber-700 font-bold text-lg">{{ $t('new_items', 'NOVÉ') }}</span>
            </div>
          </div>
          <div v-show="!isPendingBookingsCollapsed">
            <div>
              <ShowBooking
                v-for="booking in pendingBookings.slice(0, 2)"
                :key="booking.id"
                :booking="booking"
                :compact="false"
                :notification="true"
                @confirm="handleConfirmBooking"
                @cancel="handleCancelBooking"
                @convert-to-work="handleConvertToWork"
              />
            </div>
          </div>
        </div>
        
        <!-- Company Invitations if any -->
         <CompanyConnections 
          v-if="hasPendingInvitations" 
          :pendingContracts="pendingInvitations"
          :embedded="true"
          :notification="true"
          @invitation-accepted="handleInvitationAccepted"
        />
        
      </div>
      
      <!-- Owner section -->
      <OwnerMainbox v-if="isManager" class="bg-amber-50 border-l-4 border-amber-500 p-0 mb-4 max-w-[500px]" compact="true" />

      <!-- Time tracking section -->
      <TimeTracking />

      <!-- Today's Work section -->
      <div v-if="showWorksMenu" class="card">
        <div class="card-header">
          <h2 class="text-title">{{ $t('todays_work', "Práce na dnes") }}</h2>
          <div class="flex items-center gap-2">
            <button
              v-if="$refs.todayWorkSection && $refs.todayWorkSection.isWorking"
              @click="$refs.todayWorkSection.openWorkActivityModal()"
              class="text-link-action blue"
            >
              {{ $t('works.other_works', 'Jiné práce') }}
            </button>
            <button @click="toggleTodayWork" class="btn-icon-subtle">
              <ChevronUp v-if="!isTodayWorkCollapsed" :size="18" />
              <ChevronDown v-else :size="18" />
            </button>
          </div>
        </div>
        <div v-show="!isTodayWorkCollapsed" class="card-content">
          <TodayWorkSection ref="todayWorkSection" @work-activity-changed="handleWorkActivityChanged" />
        </div>
      </div>

      <!-- Performed Activities Log section -->
      <div v-if="showWorksMenu" class="card">
        <PerformedActivitiesLog ref="performedActivitiesLog" />
      </div>

      <!-- Long-term Projects section -->
      <!-- <div v-if="hasUnscheduledWorks" class="card">
        <div class="card-header">
          <h2 class="text-title">{{ $t('long_term_projects', 'Dlouhodobé projekty') }}</h2>
          <div class="flex items-center gap-2">
            <button @click="toggleLongTermProjects" class="btn-icon-subtle">
              <ChevronUp v-if="!isLongTermProjectsCollapsed" :size="18" />
              <ChevronDown v-else :size="18" />
            </button>
          </div>
        </div>
        <div v-show="!isLongTermProjectsCollapsed" class="card-content">
          <div class="space-y-2">
            <div
              v-for="work in unscheduledWorks"
              :key="'unscheduled-' + work.id"
              class="p-3 rounded-lg border bg-white border-gray-200"
            >
              <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3">
                  Status Indicator
                  <div class="p-2 bg-gray-100 rounded-lg">
                  </div>

                  Work Details
                  <div class="min-w-0 flex-1">
                    <div class="flex items-center gap-2 mb-1">
                      <p class="text-base font-medium text-gray-800">
                        {{ work.title }}
                      </p>
                    </div>

                    Location
                    <p v-if="work.location" class="text-sm flex items-center mt-1">
                      <MapPin :size="12" class="mr-1 flex-shrink-0" />
                      <span class="break-words">{{ work.location }}</span>
                    </p>

                    Assigned Users
                    <div v-if="work.work_assignments && work.work_assignments.length > 0" class="text-sm  mt-1">
                      {{ work.work_assignments.map(wa => wa.contract.first_name + ' ' + wa.contract.last_name).join(', ') }}
                    </div>

                    Long-term indicator
                    <p class="text-xs text-gray-500 mt-1">
                      {{ $t('no_specific_date', 'Bez konkrétního termínu') }}
                    </p>
                  </div>
                </div>

                Action Buttons
                <div class="flex flex-col gap-1 flex-shrink-0">
                  <button
                    @click="$refs.todayWorkSection.quickStartWork(work)"
                    class="btn btn-small btn-primary text-xs px-2 py-1"
                  >
                    {{ $t('start_work', 'Začít') }}
                  </button>
                  <button
                    @click="$refs.todayWorkSection.openWorkDetails(work)"
                    class="btn btn-small btn-secondary text-xs px-2 py-1"
                  >
                    {{ $t('details', 'Detaily') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div> -->

    </div>

    <!-- Second Column -->
    <div class="content-spacing">
            
       <!-- Employee/Team Overview Section -->
       <div v-if="sortedEmployees && sortedEmployees.length > 0" class="card">
        <div class="card-header">
          <h2 class="text-title">{{ $t('team_at_work', 'V práci') }}</h2>
          <div class="flex items-center gap-2">
            <button @click="toggleTeamOverview" class="btn-icon-subtle">
              <ChevronUp v-if="!isTeamOverviewCollapsed" :size="18" />
              <ChevronDown v-else :size="18" />
            </button>
          </div>
        </div>
        <div v-show="!isTeamOverviewCollapsed" class="card-content">
          <div class="item-list">
            <div 
              v-for="employee in sortedEmployees" 
              :key="employee.id" 
              class="item-list-item"
            >
              <div class="status-indicator" :class="{
                'status-indicator-active': employee.working,
                'animate-ping-strong': employee.justUpdated
              }"></div>
              <div class="avatar-placeholder-sm flex items-center justify-center">
                <UserCircle :size="20" class="text-gray-500" />
              </div>
              <div class="flex-1">
                <div class="text-body">{{ employee.name }}</div>
                <div v-if="employee.working">
                  <div v-if="employee.current_work" class="text-caption text-blue-600">
                    {{ employee.current_work.title }}
                    <span v-if="employee.start">{{ $t('since', 'od') }} {{ formatDateTime(employee.start) }}</span>
                  </div>
                  <div v-if="employee.current_work && employee.current_work.location" class="text-caption text-gray-500">
                    {{ employee.current_work.location }}
                  </div>
                  <div v-else-if="!employee.current_work" class="text-caption text-green-600">
                    {{ $t('at_work', 'V práci') }}
                    <span v-if="employee.start">{{ $t('since', 'od') }} {{ formatDateTime(employee.start) }}</span>
                  </div>
                </div>
                <div class="text-caption text-gray-400" v-else>
                  {{ $t('out_of_office', 'Mimo práci') }}
                  <span v-if="employee.end">{{ $t('since', 'od') }} {{ formatDateTime(employee.end) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <div class="flex items-center justify-between gap-4">
            <div v-if="sortedEmployees.length > 5" class="text-center">
              <LocalizedLink :to="'/contracts'" class="text-link-action blue" :use-anchor="true">{{ $t('show_all', 'Zobrazit všechny') }}</LocalizedLink>
            </div>
            <div v-if="isManager">
              <a href="#" class="text-link-action blue" @click.prevent="openContractForm">{{ $t('add_colleague', 'Přidat kolegu') }}</a>
            </div>
          </div>
        </div>
      </div>
      
    </div>

    <!-- Third Column -->
    <div class="content-spacing">
      <!-- Booking Links -->
      <div v-if="hasPlus && bookingLinks && bookingLinks.length > 0" class="card">
        <div class="card-header">
          <h2 class="text-title">{{ $t('booking_pages', 'Rezervační stránky') }}</h2>
          <div class="flex items-center gap-2">
            <LocalizedLink :to="'/bookings'" class="text-link-action blue" :use-anchor="true">{{ $t('show_all', 'Zobrazit vše') }}</LocalizedLink>
            <button @click="toggleBookingLinks" class="btn-icon-subtle">
              <ChevronUp v-if="!isBookingLinksCollapsed" :size="18" />
              <ChevronDown v-else :size="18" />
            </button>
          </div>
        </div>
        <div v-show="!isBookingLinksCollapsed" class="card-content">
          <ShowBookingLink 
            v-for="link in bookingLinks.slice(0, 3)" 
            :key="link.id" 
            :bookingLink="link" 
            :companySlug="companySlug"
            :compact="true" 
          />
        </div>
      </div>

    </div>
  </div>

  <!-- Subtle Feedback Button - Fixed position bottom right -->
  <div class="feedback-button-container">
    <FeedbackButton />
  </div>

  <!-- Contract Modal -->
  <div v-if="showContractModal" class="modal-overlay">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3>{{ activeContractId ? $t('edit_contract', 'Upravit kontrakt') : $t('new_contract', 'Nový kontrakt') }}</h3>
        <button @click="closeContractModal" class="close-btn">&times;</button>
      </div>
      <div class="central-modal-content"> 
        <ContractForm 
          v-if="showContractModal" 
          :contract-id="activeContractId"
          :current-user-id="userId" 
          :user-role="userRole"
          @saved="handleContractSaved"
          @cancel="closeContractModal"
        />
      </div>
    </div>
  </div>

</template>

<script>
import axios from 'axios';
import { mapGetters, mapActions } from 'vuex';
import { dateFormatter } from '../utils/dateFormatter';
import authorizationMixin from '../mixins/authorizationMixin';
import { sendFlashMessage } from '@/utils/flashMessage';
import cable from '../services/cable';
import TimeTracking from './TimeTracking.vue';
import TodayWorkSection from './TodayWorkSection.vue';
import PerformedActivitiesLog from './PerformedActivitiesLog.vue';
import OwnerMainbox from './OwnerMainbox.vue';
import RefreshButton from './shared/RefreshButton.vue';
import CompanyConnections from './CompanyConnections.vue';
import ShowBookingLink from './bookings/ShowBookingLink.vue';
import ShowBooking from './bookings/ShowBooking.vue';
import FeedbackButton from './feedback/FeedbackButton.vue';
import SystemMessage from './SystemMessage.vue';
import {
  Clock, ChevronDown, ChevronUp, ChevronLeft, ChevronRight,
  Menu, Search, Bell, LogOut, Crown, PanelLeft, BarChart3,
  Warehouse, Calendar, Mailbox, Settings, LayoutDashboard, 
  CalendarDays, LandPlot, UsersRound, CalendarCheck, 
  FileText, UserCircle, X, Briefcase, MapPin
} from 'lucide-vue-next';
import ContractForm from './contracts/ContractForm.vue';
import LocalizedLink from './LocalizedLink.vue';


export default {
  name: 'Mainbox',
  mixins: [authorizationMixin],
  components: {
    TimeTracking,
    TodayWorkSection,
    PerformedActivitiesLog,
    OwnerMainbox,
    RefreshButton,
    CompanyConnections,
    ShowBookingLink,
    ShowBooking,
    SystemMessage,
    Clock, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, Menu, Search, Bell, LogOut, Crown, PanelLeft,
    BarChart3, Warehouse, Calendar, Mailbox, Settings, LayoutDashboard, CalendarDays, LandPlot,
    UsersRound, CalendarCheck, FileText, UserCircle, X, Briefcase, MapPin,
    ContractForm,
    LocalizedLink,
    FeedbackButton
  },
  props: {
    subscription: {
      type: Object,
      default: () => ({ current_plan: null, available_features: [] })
    }
  },
  data() {
    return {
      teamMembers: [],
      pendingInvitations: [],
      pendingBookings: [],
      pendingNotifications: [],
      loading: false,
      sortKey: 'name',
      bookingLinks: [],
      companyName: '',
      companySlug: '',
      teamSummary: null,
      isWelcomeContentCollapsed: false,
      isTeamOverviewCollapsed: false,
      isPendingBookingsCollapsed: false,
      isBookingLinksCollapsed: false,
      isLongTermProjectsCollapsed: false,
      isTodayWorkCollapsed: false,
      unscheduledWorks: [],
      showContractModal: false,
      activeContractId: null,
      teamStatusSubscription: null,
    };
  },

  computed: {
    ...mapGetters('userStore', ['userRole', 'email', 'userId']),
    ...mapGetters('logStore', ['hasOpenRecords']),
    ...mapGetters('ownerStore', ['employees']),
    
    formattedDate() {
      const locale = dateFormatter.getLocaleString(this.$i18n.locale);
      return dateFormatter.longDate(new Date(), locale);
    },
    
    hasPendingInvitations() {
      return this.pendingInvitations.length > 0;
    },
    hasPendingBookings() {
      // Only show pending bookings if bookings menu is enabled
      return this.showBookingsMenu && this.pendingBookings?.filter(booking => booking.status === 'pending').length > 0;
    },
    hasPendingNotifications() {
      // return this.pendingNotifications.length > 0;
      return false; // Always return false while notifications are disabled
    },
    sortedEmployees() {
      // ALL users now use Vuex store employees for consistency with WebSocket updates
      const employeeList = this.employees;
      return employeeList.slice().sort((a, b) => {
        if (a.working && !b.working) return -1;
        if (!a.working && b.working) return 1;
        return a.name.localeCompare(b.name);
      });
    },
    hasPlus() {
      return ['plus', 'premium'].includes(this.subscription.current_plan);
    },
    hasPremium() {
      return this.subscription.current_plan === 'premium';
    },

    hasUnscheduledWorks() {
      // Only show unscheduled works if works menu is enabled
      return this.showWorksMenu && this.unscheduledWorks.length > 0;
    },
    greeting() {
      const hour = new Date().getHours();
      if (hour < 12) return this.$t('good_morning', 'Dobré ráno!');
      if (hour < 18) return this.$t('good_afternoon', 'Dobrý den!');
      return this.$t('good_evening', 'Dobrý večer!');
    },
    hasPendingItems() {
      return this.hasPendingInvitations || this.hasPendingBookings || this.hasPendingNotifications;
    },
    pendingItems() {
      let items = [];
      if (this.hasPendingInvitations) {
        items.push(this.$t('invitations_count', 'pozvání: {count}', { count: this.pendingInvitations.length }));
      }
      if (this.hasPendingBookings) {
        const count = this.pendingBookings.filter(b => b.status === 'pending').length;
        items.push(this.$t('bookings_count', 'rezervace: {count}', { count }));
      }
      // if (this.hasPendingNotifications) {
      //   items.push(this.$t('notifications_count', 'oznámení: {count}', { count: this.pendingNotifications.length }));
      // }
      return items.join(this.$t('and_joiner', ' a '));
    }
  },

  methods: {
    ...mapActions('ownerStore', ['fetchEmployees', 'handleTeamStatusUpdate']),
    
    formatDateTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleString('cs-CZ', { 
        day: '2-digit',
        month: '2-digit',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    },
    getLocalStorageBoolean(key, defaultValue = false) {
      const storedValue = localStorage.getItem(key);
      if (storedValue === null) {
        return defaultValue;
      }
      try {
        const parsedValue = JSON.parse(storedValue); 
        return typeof parsedValue === 'boolean' ? parsedValue : defaultValue;
      } catch (e) {
        console.warn(`Could not parse localStorage key "${key}":`, e);
        return defaultValue;
      }
    },
    
    toggleWelcomeContent() {
      this.isWelcomeContentCollapsed = !this.isWelcomeContentCollapsed;
      localStorage.setItem('mainbox_welcome_collapsed', JSON.stringify(this.isWelcomeContentCollapsed));
    },
    toggleTeamOverview() {
      this.isTeamOverviewCollapsed = !this.isTeamOverviewCollapsed;
      localStorage.setItem('mainbox_team_collapsed', JSON.stringify(this.isTeamOverviewCollapsed));
    },
    togglePendingBookings() {
      this.isPendingBookingsCollapsed = !this.isPendingBookingsCollapsed;
      localStorage.setItem('mainbox_bookings_collapsed', JSON.stringify(this.isPendingBookingsCollapsed));
    },
    toggleBookingLinks() {
      this.isBookingLinksCollapsed = !this.isBookingLinksCollapsed;
      localStorage.setItem('mainbox_links_collapsed', JSON.stringify(this.isBookingLinksCollapsed));
    },

    toggleLongTermProjects() {
      this.isLongTermProjectsCollapsed = !this.isLongTermProjectsCollapsed;
      localStorage.setItem('mainbox_longterm_collapsed', JSON.stringify(this.isLongTermProjectsCollapsed));
    },

    toggleTodayWork() {
      this.isTodayWorkCollapsed = !this.isTodayWorkCollapsed;
      localStorage.setItem('mainbox_todaywork_collapsed', JSON.stringify(this.isTodayWorkCollapsed));
    },





    handleWorkActivityChanged() {
      // Refresh unscheduled works to reflect any changes (only if works menu is enabled)
      if (this.showWorksMenu) {
        this.fetchUnscheduledWorks();
      }

      // Refresh team members to show updated work status
      this.fetchTeamMembers();

      // Refresh team summary if available
      if (this.isManager && this.hasPlus) {
        this.fetchTeamSummary();
      }

      // Refresh performed activities log
      if (this.$refs.performedActivitiesLog) {
        this.$refs.performedActivitiesLog.refreshActivities();
      }
    },
    
    async fetchPendingInvitations() {
      try {
        this.loading = true;
        const response = await axios.get('/company_connections/fetch');
        this.pendingInvitations = response.data;
      } catch (error) {
        console.error('Error fetching invitations:', error);
      } finally {
        this.loading = false;
      }
    },

    async handleInvitationAccepted(contractId) {
      // Remove the accepted invitation from the local data
      this.pendingInvitations = this.pendingInvitations.filter(invitation => invitation.id !== contractId);
      
      // Optionally refresh data from server to ensure consistency
      // await this.fetchPendingInvitations();
    },
    async fetchPendingBookings() {
      try {
        const response = await axios.get('/bookings/fetch', { params: { status: 'pending' } });
        this.pendingBookings = response.data.bookings;
      } catch (error) {
        console.error('Error fetching bookings:', error);
      }
    },
    async fetchTeamMembers() {
      // ALL users (managers and employees) should use Vuex store for consistency
      // This ensures WebSocket updates are reflected for everyone
      await this.fetchEmployees();
    },
    async fetchTeamSummary() {
      if (!this.isManager || !this.hasPlus) return;
      
      try {
        const response = await axios.get('/daily_logs/team_summary');
        this.teamSummary = response.data;
      } catch (error) {
        console.error('Error fetching team summary:', error);
      }
    },
    async fetchBookingLinks() {
      try {
        const response = await axios.get('/booking_links');
        this.bookingLinks = response.data.booking_links;
        this.companySlug = response.data.company_slug;
        this.companyName = response.data.company_name;
        console.log(response.data)
      } catch (error) {
        console.error('Error fetching booking links:', error);
      }
    },

    async fetchUnscheduledWorks() {
      try {
        const response = await axios.get('/api/v1/works/assigned', {
          headers: { 'Accept': 'application/json' }
        });
        const allAssignedWorks = Array.isArray(response.data) ? response.data : [];

        // Filter unscheduled works (works without scheduled dates)
        this.unscheduledWorks = allAssignedWorks.filter(work =>
          !work.scheduled_start_date && !work.scheduled_end_date
        );
        console.log('Unscheduled works:', this.unscheduledWorks);
      } catch (error) {
        console.error('Error fetching unscheduled works:', error);
        this.unscheduledWorks = [];
      }
    },
    async handleConfirmBooking(booking, options = {}) {
      console.log(options);
      try {
        const bookingData = { 
          specific_time: options.specificTime || null,
          preferred_date: options.preferredDate || null
        };
        const response = await axios.post(`/bookings/${booking.id}/confirm`, { booking: bookingData });
        if (response.data.success) {
          const index = this.pendingBookings.findIndex(b => b.id === booking.id);
          if (index !== -1) {
            this.pendingBookings[index] = response.data.booking;
          }
        }
      } catch (error) {
        console.error('Error confirming booking:', error);
      }
    },
    async handleCancelBooking(booking) {
      try {
        const response = await axios.post(`/bookings/${booking.id}/cancel`);
        if (response.data.success) {
          const index = this.pendingBookings.findIndex(b => b.id === booking.id);
          if (index !== -1) {
            this.pendingBookings[index] = response.data.booking;
          }
        }
      } catch (error) {
        console.error('Error cancelling booking:', error);
      }
    },
    async handleConvertToWork(booking) {
      try {
        const response = await axios.post(`/bookings/${booking.id}/convert_to_work`);
        if (response.data.success) {
          const index = this.pendingBookings.findIndex(b => b.id === booking.id);
          if (index !== -1) {
            this.pendingBookings[index] = response.data.booking;
          }
        }
      } catch (error) {
        console.error('Error converting booking to work:', error);
      }
    },
    openAddEventModal() {
      const eventDetail = {
        detail: {
          componentName: 'EventForm',
          title: this.$t('add_event', 'Přidat událost'),
          props: {
            initialDate: null
          } 
        }
      }
      document.dispatchEvent(new CustomEvent('open-central-modal', eventDetail));
    },
    openContractForm() {
      this.activeContractId = null; 
      this.showContractModal = true;
    },
    closeContractModal() {
      this.showContractModal = false;
      setTimeout(() => {
        this.activeContractId = null;
      }, 300);
    },
    handleContractSaved() {
      this.fetchTeamMembers(); 
      this.closeContractModal();
    },
    async fetchPendingNotifications() {
      // Commented out notification system for now
      // try {
      //   const response = await axios.get('/api/v1/notifications/unread');
      //   this.pendingNotifications = response.data.notifications;
      // } catch (error) {
      //   console.error('Error fetching notifications:', error);
      // }
    },

    subscribeToTeamStatus() {
      // Use imported cable service
      if (!cable) {
        console.warn('[Mainbox] Cable service not available for team status subscription');
        return;
      }

      try {
        // Subscribe to TeamStatusChannel
        this.teamStatusSubscription = cable.subscribe('TeamStatusChannel', {}, {
          connected: () => {
            console.log('[Mainbox] Connected to TeamStatusChannel');
          },
          disconnected: () => {
            console.log('[Mainbox] Disconnected from TeamStatusChannel');
          },
          received: (data) => {
            console.log('[Mainbox] Received team status update:', data);
            // Use Vuex action to handle the update
            this.handleTeamStatusUpdate(data);
          }
        });
      } catch (error) {
        console.error('[Mainbox] Failed to subscribe to TeamStatusChannel:', error);
      }
    },
  },

  async mounted() {
    // Load collapsed states from localStorage
    this.isWelcomeContentCollapsed = this.getLocalStorageBoolean('mainbox_welcome_collapsed');
    this.isTeamOverviewCollapsed = this.getLocalStorageBoolean('mainbox_team_collapsed');
    this.isPendingBookingsCollapsed = this.getLocalStorageBoolean('mainbox_bookings_collapsed');
    this.isBookingLinksCollapsed = this.getLocalStorageBoolean('mainbox_links_collapsed');
    this.isLongTermProjectsCollapsed = this.getLocalStorageBoolean('mainbox_longterm_collapsed');
    this.isTodayWorkCollapsed = this.getLocalStorageBoolean('mainbox_todaywork_collapsed');

    // Fetch data
    this.fetchTeamMembers();
    this.fetchPendingInvitations();
    this.fetchTeamSummary();
    // this.fetchPendingNotifications();
    
    // Fetch pending bookings only if bookings menu is enabled
    if (this.showBookingsMenu) {
      this.fetchPendingBookings();

      // Booking links are a premium feature
      if (this.hasPlus) {
        this.fetchBookingLinks();
      }
    }

    // TODO: Add meetings data fetching when implemented
    // if (this.showMeetingsMenu) {
    //   this.fetchPendingMeetings();
    // }

    // Fetch unscheduled works only if works menu is enabled
    if (this.showWorksMenu) {
      this.fetchUnscheduledWorks();
    }

    // Subscribe to real-time team status updates for ALL users (employees and managers)
    // This enables real-time collaboration between all team members
    this.subscribeToTeamStatus();
  },

  beforeUnmount() {
    // Unsubscribe from WebSocket channel
    if (this.teamStatusSubscription) {
      cable.unsubscribe('TeamStatusChannel');
      this.teamStatusSubscription = null;
    }
  }
};
</script>

<style scoped>
.feedback-button-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.feedback-button-container:hover {
  opacity: 1;
}

/* Mobile positioning - flows with content, no overlay */
@media (max-width: 768px) {
  .feedback-button-container {
    position: static;
    margin-top: 40px;
    margin-bottom: 20px;
    text-align: right;
    opacity: 0.8;
  }
}

@keyframes ping-strong {
  0% {
    transform: scale(1);
    background-color: #888; /* gray */
    opacity: 1;
  }
  80% {
    transform: scale(2);
    opacity: 0;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping-strong {
  position: relative;
}

.animate-ping-strong::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 9999px;
  border: 2px solid rgba(34, 197, 94, 0.8); /* green-500 */
  animation: ping-strong 0.8s ease-out 3; /* 3 quick pulses */
}
</style>
